# LstmSync 项目部署指南

## 📋 系统要求

### 硬件要求
- **GPU**: NVIDIA显卡，4GB+显存（推荐A10或更高）
- **内存**: 8GB+ RAM
- **存储**: 10GB+ 可用空间

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+ 推荐)
- **CUDA**: 12.4+ (与PyTorch兼容)
- **Python**: 3.8 (A10显卡) 或 3.10 (50系显卡)
- **Conda**: 用于环境管理

## 🚀 快速部署步骤

### 1. 环境准备

#### 1.1 创建Conda虚拟环境
```bash
# 创建Python 3.8环境（A10显卡）
conda create -n lstmsync python=3.8 -y

# 激活环境
conda activate lstmsync
```

#### 1.2 验证基础环境
```bash
# 验证Python版本
python --version  # 应显示 Python 3.8.x

# 验证CUDA
nvcc --version    # 应显示 CUDA 12.4+
nvidia-smi        # 查看GPU信息
```

### 2. 安装PyTorch

```bash
# 安装与CUDA 12.4兼容的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
```

#### 验证PyTorch安装
```bash
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'CUDA版本: {torch.version.cuda}')
print(f'GPU数量: {torch.cuda.device_count()}')
if torch.cuda.is_available():
    print(f'GPU名称: {torch.cuda.get_device_name(0)}')
"
```

### 3. 安装项目依赖

#### 3.1 选择合适的requirements文件
```bash
# A10显卡使用 requirements.txt
pip install -r requirements.txt

# 50系显卡使用 requirements-50.txt
# pip install -r requirements-50.txt
```

#### 3.2 验证关键依赖
```bash
python -c "
import cv2, numpy as np, librosa, transformers, insightface, onnxruntime as ort
print('所有依赖安装成功！')
print(f'ONNX Runtime GPU支持: {\"CUDAExecutionProvider\" in ort.get_available_providers()}')
"
```

### 4. 验证ffmpeg

```bash
# 检查ffmpeg是否可用
ffmpeg -version
```

### 5. 运行项目测试

```bash
# 执行示例脚本
python run.py
```

## 📁 项目结构说明

```
LstmSync/
├── checkpoints/                    # 模型权重目录
│   ├── 192jm.pth                  # 同步权重文件
│   ├── chinese-hubert-large/      # 音频模型权重
│   └── auxiliary/                 # 辅助模型文件
├── key.txt                        # 必需的密钥文件
├── lstmsync_func.cpython-38-x86_64-linux-gnu.so  # Linux Python 3.8 库
├── lstmsync_func.cpython-310-x86_64-linux-gnu.so # Linux Python 3.10 库
├── requirements.txt               # A10显卡依赖
├── requirements-50.txt            # 50系显卡依赖
├── run.py                        # 示例运行脚本
├── 1.mp4                         # 示例输入视频
├── 1.wav                         # 示例输入音频
└── res.mp4                       # 输出结果视频
```

## ⚙️ 配置参数说明

### run.py 参数配置
```python
c = lstmsync_func.LstmSync(
    batch_size=4,        # 批处理大小，影响处理速度和显存占用
    sync_offset=0,       # 音画同步偏移调节
    scale_h=1.5,         # 遮罩高度控制（推荐1.6）
    scale_w=3,           # 遮罩宽度控制（推荐3.6）
    key_file="./key.txt" # 密钥文件路径（必须存在）
)
```

### 重要参数调优
- **scale_h, scale_w**: 控制脸部遮罩范围，数值越大遮罩越小
  - 推荐设置：`scale_h=1.6, scale_w=3.6`
  - 如果脸颊左右遮罩严重，适当调大这两个值

## 🔧 故障排除

### 常见问题及解决方案

#### 1. CUDA相关问题
**问题**: `torch.cuda.is_available()` 返回 False
**解决方案**:
```bash
# 检查CUDA版本兼容性
nvcc --version
nvidia-smi

# 重新安装正确版本的PyTorch
pip uninstall torch torchvision torchaudio
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
```

#### 2. 依赖安装失败
**问题**: insightface 编译失败
**解决方案**:
```bash
# 安装编译依赖
sudo apt-get update
sudo apt-get install build-essential cmake

# 或使用预编译版本
pip install insightface==0.7.3 --no-deps
```

#### 3. 内存不足
**问题**: GPU显存不足
**解决方案**:
- 减小 `batch_size` 参数
- 确保没有其他程序占用GPU
- 使用 `nvidia-smi` 监控显存使用

#### 4. ffmpeg问题
**问题**: ffmpeg命令不可用
**解决方案**:
```bash
# Ubuntu/Debian
sudo apt-get install ffmpeg

# 或下载静态版本
wget https://johnvansickle.com/ffmpeg/builds/ffmpeg-git-amd64-static.tar.xz
```

#### 5. 权限问题
**问题**: 文件权限错误
**解决方案**:
```bash
# 确保关键文件有执行权限
chmod +x lstmsync_func.cpython-38-x86_64-linux-gnu.so
chmod 644 key.txt
```

## 📊 性能优化建议

### 1. 硬件优化
- 使用SSD存储提高I/O性能
- 确保充足的系统内存
- 保持GPU温度在合理范围

### 2. 软件优化
- 关闭不必要的后台程序
- 使用最新的NVIDIA驱动
- 定期清理临时文件

### 3. 参数调优
- 根据GPU显存调整batch_size
- 根据视频质量需求调整scale参数
- 合理设置sync_offset进行音画同步

## 🔍 环境验证脚本

创建验证脚本 `verify_env.py`:
```python
#!/usr/bin/env python3
import sys
import torch
import cv2
import numpy as np
import librosa
import transformers
import insightface
import onnxruntime as ort

def verify_environment():
    print("=== LstmSync 环境验证 ===")
    
    # Python版本
    print(f"Python版本: {sys.version}")
    
    # PyTorch和CUDA
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        print(f"GPU名称: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 关键依赖版本
    print(f"OpenCV版本: {cv2.__version__}")
    print(f"NumPy版本: {np.__version__}")
    print(f"Librosa版本: {librosa.__version__}")
    print(f"Transformers版本: {transformers.__version__}")
    print(f"InsightFace版本: {insightface.__version__}")
    print(f"ONNX Runtime版本: {ort.__version__}")
    
    # ONNX Runtime GPU支持
    providers = ort.get_available_providers()
    print(f"ONNX Runtime GPU支持: {'CUDAExecutionProvider' in providers}")
    
    print("=== 验证完成 ===")

if __name__ == "__main__":
    verify_environment()
```

运行验证:
```bash
python verify_env.py
```

## 📞 技术支持

如遇到问题，请提供以下信息：
1. 系统信息 (`uname -a`)
2. GPU信息 (`nvidia-smi`)
3. Python环境 (`python --version`, `pip list`)
4. 错误日志完整输出

## 🎯 下一步

部署成功后，您可以：
1. 修改 `run.py` 中的参数进行自定义配置
2. 替换输入的视频和音频文件
3. 根据需要调整遮罩参数获得更好效果
4. 集成到您的应用程序中

---

**注意**: 本指南基于Ubuntu 20.04和NVIDIA A10显卡环境测试，其他环境可能需要适当调整。
