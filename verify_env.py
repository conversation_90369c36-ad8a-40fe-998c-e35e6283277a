#!/usr/bin/env python3
"""
LstmSync 环境验证脚本
用于验证项目运行环境是否正确配置
"""

import sys
import subprocess
import os

def check_command(cmd):
    """检查命令是否可用"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout.strip()
    except:
        return False, ""

def verify_environment():
    print("=" * 50)
    print("LstmSync 环境验证脚本")
    print("=" * 50)
    
    # 1. 检查Python版本
    print(f"\n1. Python环境:")
    print(f"   Python版本: {sys.version}")
    python_version = sys.version_info
    if python_version.major == 3 and python_version.minor in [8, 10]:
        print("   ✅ Python版本符合要求")
    else:
        print("   ❌ Python版本不符合要求（需要3.8或3.10）")
    
    # 2. 检查CUDA
    print(f"\n2. CUDA环境:")
    cuda_available, cuda_output = check_command("nvcc --version")
    if cuda_available:
        print(f"   ✅ CUDA可用")
        print(f"   版本信息: {cuda_output.split('release')[1].split(',')[0].strip() if 'release' in cuda_output else '未知'}")
    else:
        print("   ❌ CUDA不可用")
    
    # 3. 检查GPU
    print(f"\n3. GPU信息:")
    gpu_available, gpu_output = check_command("nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits")
    if gpu_available:
        print("   ✅ GPU可用")
        for line in gpu_output.split('\n'):
            if line.strip():
                parts = line.split(',')
                if len(parts) >= 2:
                    name = parts[0].strip()
                    memory = parts[1].strip()
                    print(f"   GPU: {name}, 显存: {memory}MB")
    else:
        print("   ❌ GPU不可用或nvidia-smi命令失败")
    
    # 4. 检查ffmpeg
    print(f"\n4. ffmpeg:")
    ffmpeg_available, ffmpeg_output = check_command("ffmpeg -version")
    if ffmpeg_available:
        version_line = ffmpeg_output.split('\n')[0] if ffmpeg_output else ""
        print(f"   ✅ ffmpeg可用: {version_line}")
    else:
        print("   ❌ ffmpeg不可用")
    
    # 5. 检查Python依赖
    print(f"\n5. Python依赖检查:")
    dependencies = {
        'torch': 'torch',
        'torchvision': 'torchvision',
        'torchaudio': 'torchaudio',
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'librosa': 'librosa',
        'transformers': 'transformers',
        'insightface': 'insightface',
        'onnxruntime-gpu': 'onnxruntime'
    }

    missing_deps = []
    for dep_name, import_name in dependencies.items():
        try:
            __import__(import_name)
            print(f"   ✅ {dep_name}")
        except ImportError:
            print(f"   ❌ {dep_name} - 未安装")
            missing_deps.append(dep_name)
    
    # 6. 检查PyTorch CUDA支持
    print(f"\n6. PyTorch CUDA支持:")
    try:
        import torch
        if torch.cuda.is_available():
            print(f"   ✅ PyTorch CUDA可用")
            print(f"   PyTorch版本: {torch.__version__}")
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            if torch.cuda.device_count() > 0:
                print(f"   GPU名称: {torch.cuda.get_device_name(0)}")
        else:
            print("   ❌ PyTorch CUDA不可用")
    except ImportError:
        print("   ❌ PyTorch未安装")
    
    # 7. 检查ONNX Runtime GPU支持
    print(f"\n7. ONNX Runtime GPU支持:")
    try:
        import onnxruntime as ort
        providers = ort.get_available_providers()
        if 'CUDAExecutionProvider' in providers:
            print("   ✅ ONNX Runtime GPU支持可用")
            print(f"   可用提供者: {', '.join(providers)}")
        else:
            print("   ❌ ONNX Runtime GPU支持不可用")
            print(f"   可用提供者: {', '.join(providers)}")
    except ImportError:
        print("   ❌ ONNX Runtime未安装")
    
    # 8. 检查项目文件
    print(f"\n8. 项目文件检查:")
    required_files = [
        'key.txt',
        'run.py',
        'requirements.txt',
        'checkpoints/192jm.pth',
        'checkpoints/chinese-hubert-large',
        'lstmsync_func.cpython-38-x86_64-linux-gnu.so'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 文件不存在")
    
    # 总结
    print(f"\n" + "=" * 50)
    print("验证总结:")
    
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
    
    try:
        import torch
        if not torch.cuda.is_available():
            print("❌ PyTorch CUDA支持不可用，请检查CUDA安装和PyTorch版本")
    except:
        pass
    
    if not cuda_available:
        print("❌ CUDA环境有问题，请检查NVIDIA驱动和CUDA安装")
    
    if not ffmpeg_available:
        print("❌ ffmpeg不可用，请安装ffmpeg")
    
    print("\n如果所有检查都通过，可以运行: python run.py")
    print("=" * 50)

if __name__ == "__main__":
    verify_environment()
