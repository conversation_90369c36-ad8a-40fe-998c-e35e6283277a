#!/bin/bash

# LstmSync 快速部署脚本
# 适用于 Ubuntu 20.04+ 系统，NVIDIA A10 显卡

set -e  # 遇到错误立即退出

echo "=========================================="
echo "LstmSync 快速部署脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   print_warning "建议不要使用root用户运行此脚本"
fi

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    print_error "conda 未安装，请先安装 Miniconda 或 Anaconda"
    exit 1
fi

print_info "检查到 conda 已安装"

# 检查NVIDIA驱动
if ! command -v nvidia-smi &> /dev/null; then
    print_error "nvidia-smi 不可用，请检查NVIDIA驱动安装"
    exit 1
fi

print_info "检查到 NVIDIA 驱动已安装"

# 检查CUDA
if ! command -v nvcc &> /dev/null; then
    print_error "nvcc 不可用，请检查CUDA安装"
    exit 1
fi

CUDA_VERSION=$(nvcc --version | grep "release" | sed 's/.*release \([0-9]\+\.[0-9]\+\).*/\1/')
print_info "检查到 CUDA 版本: $CUDA_VERSION"

# 检查ffmpeg
if ! command -v ffmpeg &> /dev/null; then
    print_warning "ffmpeg 未安装，正在安装..."
    sudo apt-get update
    sudo apt-get install -y ffmpeg
    print_info "ffmpeg 安装完成"
else
    print_info "检查到 ffmpeg 已安装"
fi

# 创建conda环境
ENV_NAME="lstmsync"
print_info "创建 conda 环境: $ENV_NAME"

if conda env list | grep -q "^$ENV_NAME "; then
    print_warning "环境 $ENV_NAME 已存在，是否删除重建？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        conda env remove -n $ENV_NAME -y
        print_info "已删除旧环境"
    else
        print_info "使用现有环境"
    fi
fi

if ! conda env list | grep -q "^$ENV_NAME "; then
    conda create -n $ENV_NAME python=3.8 -y
    print_info "conda 环境创建完成"
fi

# 激活环境
print_info "激活 conda 环境"
source $(conda info --base)/etc/profile.d/conda.sh
conda activate $ENV_NAME

# 验证Python版本
PYTHON_VERSION=$(python --version)
print_info "Python 版本: $PYTHON_VERSION"

# 安装PyTorch
print_info "安装 PyTorch (CUDA 12.4)"
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124

# 验证PyTorch安装
print_info "验证 PyTorch 安装"
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU数量: {torch.cuda.device_count()}')
    print(f'GPU名称: {torch.cuda.get_device_name(0)}')
else:
    print('ERROR: CUDA不可用')
    exit(1)
"

if [ $? -ne 0 ]; then
    print_error "PyTorch CUDA 验证失败"
    exit 1
fi

print_info "PyTorch 安装验证成功"

# 安装项目依赖
print_info "安装项目依赖"
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    print_info "依赖安装完成"
else
    print_error "requirements.txt 文件不存在"
    exit 1
fi

# 运行环境验证
print_info "运行环境验证脚本"
if [ -f "verify_env.py" ]; then
    python verify_env.py
else
    print_warning "verify_env.py 文件不存在，跳过验证"
fi

# 检查关键文件
print_info "检查项目文件"
REQUIRED_FILES=(
    "key.txt"
    "run.py"
    "checkpoints/192jm.pth"
    "checkpoints/chinese-hubert-large"
    "lstmsync_func.cpython-38-x86_64-linux-gnu.so"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ] || [ -d "$file" ]; then
        print_info "✅ $file"
    else
        print_error "❌ $file 不存在"
        exit 1
    fi
done

# 完成
echo ""
echo "=========================================="
print_info "部署完成！"
echo "=========================================="
echo ""
print_info "使用方法："
echo "1. 激活环境: conda activate $ENV_NAME"
echo "2. 运行项目: python run.py"
echo "3. 查看结果: ls -la res.mp4"
echo ""
print_info "如需验证环境: python verify_env.py"
echo ""
print_warning "注意事项："
echo "- 确保输入文件 1.mp4 和 1.wav 存在"
echo "- 可以修改 run.py 中的参数进行自定义配置"
echo "- 如果遇到遮罩问题，调整 scale_h 和 scale_w 参数"
echo ""
